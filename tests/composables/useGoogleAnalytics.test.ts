import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useGoogleAnalytics } from '~/composables/useGoogleAnalytics'

// Mock Nuxt composables
vi.mock('#app', () => ({
  useRuntimeConfig: () => ({
    public: {
      debug: true,
    },
  }),
}))

// Mock window.dataLayer
const mockDataLayer: any[] = []
Object.defineProperty(window, 'dataLayer', {
  value: mockDataLayer,
  writable: true,
})

describe('useGoogleAnalytics', () => {
  beforeEach(() => {
    // Clear dataLayer before each test
    mockDataLayer.length = 0
    
    // Mock import.meta.client
    vi.stubGlobal('import.meta', { client: true })
  })

  it('should initialize dataLayer', () => {
    const { initializeDataLayer } = useGoogleAnalytics()
    
    initializeDataLayer()
    
    expect(window.dataLayer).toBeDefined()
    expect(Array.isArray(window.dataLayer)).toBe(true)
  })

  it('should send events to dataLayer', () => {
    const { sendEvent } = useGoogleAnalytics()
    
    const testEvent = {
      event: 'test_event',
      category: 'Test',
      action: 'Click',
    }
    
    sendEvent(testEvent)
    
    expect(mockDataLayer).toContain(testEvent)
  })

  it('should create product objects correctly', () => {
    const { createProduct } = useGoogleAnalytics()
    
    const product = createProduct(
      'JFK - LHR',
      'JFK2LHR',
      6000.66,
      'roundTrip',
      'premium',
      0,
      1
    )
    
    expect(product).toEqual({
      name: 'JFK - LHR',
      id: 'JFK2LHR',
      price: 6000.66,
      category: 'Round Trip',
      list: 'Premium Class',
      position: 0,
      quantity: 1,
    })
  })

  it('should map class types correctly', () => {
    const { getClassDisplayName } = useGoogleAnalytics()
    
    expect(getClassDisplayName('premium')).toBe('Premium Class')
    expect(getClassDisplayName('business')).toBe('Business Class')
    expect(getClassDisplayName('first')).toBe('First Class')
  })

  it('should map trip types correctly', () => {
    const { getTypeDisplayName } = useGoogleAnalytics()
    
    expect(getTypeDisplayName('oneWay')).toBe('One Way')
    expect(getTypeDisplayName('roundTrip')).toBe('Round Trip')
    expect(getTypeDisplayName('multi')).toBe('Multi City')
  })

  it('should send custom dimensions', () => {
    const { sendDimensions } = useGoogleAnalytics()
    
    const product = {
      name: 'JFK - LHR',
      id: 'JFK2LHR',
      price: 6000.66,
      category: 'Round Trip',
      list: 'Premium Class',
      position: 0,
    }
    
    sendDimensions(product)
    
    const sentEvent = mockDataLayer[0]
    expect(sentEvent).toMatchObject({
      event: 'custom-dimensions',
      gtmUaEventCategory: 'Custom Dimensions',
      gtmUaEventAction: 'Set Dimensions',
      dimension1: 'LHR', // Destination code
      dimension2: 'Offer Detail',
      dimension3: 6000.66,
      dimension4: 'JFK', // Origin code
    })
  })

  it('should handle server-side rendering', () => {
    // Mock server-side
    vi.stubGlobal('import.meta', { client: false })
    
    const { sendEvent } = useGoogleAnalytics()
    
    const testEvent = { event: 'test_event' }
    
    // Should not throw error on server-side
    expect(() => sendEvent(testEvent)).not.toThrow()
    
    // Should not add to dataLayer on server-side
    expect(mockDataLayer).toHaveLength(0)
  })
})
