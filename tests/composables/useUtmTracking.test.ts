import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useUtmTracking } from '~/composables/useUtmTracking'

// Mock Nuxt composables
vi.mock('#app', () => ({
  useRoute: () => ({
    query: {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'test_campaign',
      gclid: '123456789',
    },
  }),
  useRuntimeConfig: () => ({
    public: {
      debug: true,
    },
  }),
}))

// Mock useGoogleAnalytics
vi.mock('~/composables/useGoogleAnalytics', () => ({
  useGoogleAnalytics: () => ({
    getGaClientId: () => '123.456',
    getDeviceId: () => 'device123',
  }),
}))

describe('useUtmTracking', () => {
  beforeEach(() => {
    // Mock import.meta.client
    vi.stubGlobal('import.meta', { client: true })
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
    }
    vi.stubGlobal('localStorage', localStorageMock)
  })

  it('should extract UTM parameters from query', () => {
    const { extractUtmFromQuery } = useUtmTracking()
    
    const query = {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'test_campaign',
      utm_content: 'ad1',
      utm_term: 'keyword',
    }
    
    const result = extractUtmFromQuery(query)
    
    expect(result).toEqual({
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'test_campaign',
      utm_content: 'ad1',
      utm_term: 'keyword',
      gclid: undefined,
      gbraid: undefined,
      gad_source: undefined,
    })
  })

  it('should detect Google Ads traffic', () => {
    const { detectGoogleAds } = useUtmTracking()
    
    // With gclid
    expect(detectGoogleAds({ gclid: '123456789' })).toBe(true)
    
    // With gbraid
    expect(detectGoogleAds({ gbraid: 'abc123' })).toBe(true)
    
    // With gad_source
    expect(detectGoogleAds({ gad_source: '1' })).toBe(true)
    
    // Without Google Ads parameters
    expect(detectGoogleAds({ utm_source: 'facebook' })).toBe(false)
  })

  it('should provide Google Ads defaults', () => {
    const { getGoogleAdsDefaults } = useUtmTracking()
    
    const defaults = getGoogleAdsDefaults()
    
    expect(defaults).toEqual({
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'not_identified',
    })
  })

  it('should create UTM visit object', () => {
    const { createUtmVisit } = useUtmTracking()
    
    // Mock current time
    const mockTime = 1640995200 // 2022-01-01 00:00:00
    vi.spyOn(Date, 'now').mockReturnValue(mockTime * 1000)
    
    // Mock document and navigator
    Object.defineProperty(document, 'referrer', {
      value: 'https://google.com',
      writable: true,
    })
    
    Object.defineProperty(window, 'location', {
      value: { href: 'https://example.com/page' },
      writable: true,
    })
    
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 Test Browser',
      writable: true,
    })
    
    const visit = createUtmVisit()
    
    expect(visit).toMatchObject({
      ga: '123.456',
      did: 'device123',
      created_at: mockTime,
      referer: 'https://google.com',
      url: 'https://example.com/page',
      ua: 'Mozilla/5.0 Test Browser',
    })
  })

  it('should generate UTM query string', () => {
    const { getUtmQueryString } = useUtmTracking()
    
    // This would need to be tested after initialization
    // For now, we'll test the concept
    expect(typeof getUtmQueryString).toBe('function')
  })

  it('should append UTM to URL', () => {
    const { appendUtmToUrl } = useUtmTracking()
    
    // Mock getUtmQueryString to return test data
    const originalGetUtmQueryString = useUtmTracking().getUtmQueryString
    vi.spyOn(useUtmTracking(), 'getUtmQueryString').mockReturnValue('utm_source=google&utm_medium=cpc')
    
    const url = 'https://example.com/page'
    const result = appendUtmToUrl(url)
    
    expect(result).toBe('https://example.com/page?utm_source=google&utm_medium=cpc')
    
    // Test with existing query parameters
    const urlWithQuery = 'https://example.com/page?existing=param'
    const resultWithQuery = appendUtmToUrl(urlWithQuery)
    
    expect(resultWithQuery).toBe('https://example.com/page?existing=param&utm_source=google&utm_medium=cpc')
  })
})
